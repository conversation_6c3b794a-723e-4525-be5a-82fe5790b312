# Protocol转换器问题解决方案

## 问题背景

在使用MapStruct转换Protocol生成的类时，经常需要添加大量的`@Mapping(target = "xxx", ignore = true)`注解来忽略Protocol生成的无效字段，如：

```java
@Mapping(target = "unknownFields", ignore = true)
@Mapping(target = "propIdBytes", ignore = true)
@Mapping(target = "mergeFrom", ignore = true)
@Mapping(target = "defaultInstanceForType", ignore = true)
@Mapping(target = "allFields", ignore = true)
@Mapping(target = "mergeUnknownFields", ignore = true)
@Mapping(target = "clearField", ignore = true)
// ... 更多重复的ignore注解
```

## 最终解决方案

经过测试发现，最简单有效的解决方案是**修改unmappedTargetPolicy策略**：

将`ReportingPolicy.ERROR`改为`ReportingPolicy.IGNORE`，这样MapStruct会自动忽略所有无法映射的字段。

## 使用方法

### 修改unmappedTargetPolicy策略

```java
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,  // 改为IGNORE
        imports = {GamePropGrantStatus.class, Date.class},
        uses = {CommonConvert.class}
)
public interface GamePropServiceConvert {

    // 只需要处理类型转换，无需大量ignore注解
    @Mapping(target = "appId", expression = "java(gamePropBean.getAppId() != null ? Long.parseLong(gamePropBean.getAppId()) : 0L)")
    @Mapping(target = "createTime", source = "createTime", qualifiedByName = "dateToLong")
    @Mapping(target = "modifyTime", source = "modifyTime", qualifiedByName = "dateToLong")
    GamePropServiceProto.GameProp convertGamePropBeanToGameProp(GamePropBean gamePropBean);
}
```

### 添加必要的类型转换方法

在`CommonConvert`中添加`@Named`注解：

```java
public class CommonConvert {

    @Named("dateToLong")
    public static Long dateToLong(Date date) {
        return date != null ? date.getTime() : null;
    }

    @Named("longToDate")
    public static Date longToDate(Long time) {
        return time != null ? new Date(time) : null;
    }
}
```

## 注解说明

### @ProtocolMapping
忽略Protocol生成类的基础无效字段：
- unknownFields
- defaultInstanceForType
- allFields
- mergeFrom
- mergeUnknownFields
- clearField
- serializedSize
- parserForType
- descriptorForType
- initializationErrorString
- initialized

### @ProtocolStringMapping
继承`@ProtocolMapping`，额外忽略字符串相关的Bytes字段：
- channelPropIdBytes
- channelGameIdBytes
- nameBytes
- propDescBytes
- operatorBytes
- remarkBytes
- iconUrlBytes
- propIdBytes
- eventNameBytes
- channelBytes
- appIdBytes
- gameIdBytes
- dataJsonBytes
- roomIdBytes
- msgBytes
- dataBytes
- userIdBytes
- uniqueIdBytes

### @ProtocolNumberMapping
继承`@ProtocolMapping`，额外忽略数字相关字段：
- memoizedHashCode
- memoizedSize

### @ProtocolCompleteMapping
继承`@ProtocolStringMapping`和`@ProtocolNumberMapping`，提供最完整的字段忽略。

## 迁移示例

### 迁移前
```java
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR, ...)
public interface GamePropServiceConvert {

    @Mapping(target = "channelPropIdBytes", ignore = true)
    @Mapping(target = "channelGameIdBytes", ignore = true)
    @Mapping(target = "unknownFields", ignore = true)
    @Mapping(target = "remarkBytes", ignore = true)
    @Mapping(target = "propDescBytes", ignore = true)
    @Mapping(target = "operatorBytes", ignore = true)
    @Mapping(target = "nameBytes", ignore = true)
    @Mapping(target = "mergeFrom", ignore = true)
    @Mapping(target = "iconUrlBytes", ignore = true)
    @Mapping(target = "defaultInstanceForType", ignore = true)
    @Mapping(target = "allFields", ignore = true)
    @Mapping(target = "mergeUnknownFields", ignore = true)
    @Mapping(target = "clearField", ignore = true)
    GamePropServiceProto.GameProp convertGamePropBeanToGameProp(GamePropBean gamePropBean);
}
```

### 迁移后
```java
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, ...)  // 关键改动
public interface GamePropServiceConvert {

    // 只需要处理必要的类型转换，Protocol无效字段自动忽略
    @Mapping(target = "appId", expression = "java(gamePropBean.getAppId() != null ? Long.parseLong(gamePropBean.getAppId()) : 0L)")
    @Mapping(target = "createTime", source = "createTime", qualifiedByName = "dateToLong")
    @Mapping(target = "modifyTime", source = "modifyTime", qualifiedByName = "dateToLong")
    GamePropServiceProto.GameProp convertGamePropBeanToGameProp(GamePropBean gamePropBean);
}
```

## 优势

1. **代码简洁**：一行配置替代十几个ignore映射
2. **维护性好**：无需管理大量重复的ignore注解
3. **编译稳定**：避免注解处理器冲突问题
4. **类型安全**：保持MapStruct的类型检查和转换能力
5. **扩展性好**：专注于业务字段的映射和类型转换

## 注意事项

1. **类型转换**：需要手动处理Bean和Proto之间的类型不匹配（如String vs Long, Date vs Long）
2. **字段映射**：如果有特殊的字段映射需求，仍需要添加`@Mapping`注解
3. **测试验证**：建议添加单元测试验证转换结果的正确性
4. **文档记录**：对于复杂的类型转换逻辑，建议添加注释说明

## 备选方案：ProtocolConvert注解类

项目中还提供了`ProtocolConvert`注解类，包含预定义的Protocol字段忽略注解，可以在需要精确控制字段映射时使用。但推荐优先使用`ReportingPolicy.IGNORE`方案。
