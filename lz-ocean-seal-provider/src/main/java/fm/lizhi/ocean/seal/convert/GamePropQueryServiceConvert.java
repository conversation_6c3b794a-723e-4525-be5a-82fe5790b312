package fm.lizhi.ocean.seal.convert;

import fm.lizhi.ocean.seal.constant.GamePropGrantStatus;
import fm.lizhi.ocean.seal.pojo.UserPropStatusResult;
import fm.lizhi.ocean.seal.pojo.PropGrantStatusResult;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 游戏道具查询服务转换器
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {GamePropGrantStatus.class, Date.class},
        uses = {CommonConvert.class}
)
public interface GamePropQueryServiceConvert {

    GamePropQueryServiceConvert INSTANCE = Mappers.getMapper(GamePropQueryServiceConvert.class);

    /**
     * 转换用户道具状态信息列表到 Proto 消息列表
     */
    List<GamePropServiceProto.UserPropStatus> convertUserPropStatusInfoListToProtoList(List<UserPropStatusResult.UserPropStatusInfo> userPropStatusInfoList);

    /**
     * 转换用户道具状态信息到 Proto 消息
     */
    @ProtocolConvert.ProtocolCompleteMapping
    GamePropServiceProto.UserPropStatus convertUserPropStatusInfoToProto(UserPropStatusResult.UserPropStatusInfo userPropStatusInfo);



    @ProtocolConvert.ProtocolCompleteMapping
    GamePropServiceProto.PropGrantDetail convertPropGrantDetailInfoToProto(PropGrantStatusResult.PropGrantDetailInfo propGrantDetailInfo);

    /**
     * 自定义转换方法，正确处理 details 列表
     */
    default GamePropServiceProto.PropGrantStatusInfo convertPropGrantStatusInfoToProto(PropGrantStatusResult.PropGrantStatusInfo propGrantStatusInfo, GamePropServiceProto.QueryPropGrantStatusParam param) {
        if (propGrantStatusInfo == null && param == null) {
            return null;
        }

        GamePropServiceProto.PropGrantStatusInfo.Builder builder = GamePropServiceProto.PropGrantStatusInfo.newBuilder();

        // 设置来自 propGrantStatusInfo 的字段
        if (propGrantStatusInfo != null) {
            if (propGrantStatusInfo.getUserId() != null) {
                builder.setUserId(propGrantStatusInfo.getUserId());
            }
            if (propGrantStatusInfo.getStatus() != null) {
                builder.setStatus(propGrantStatusInfo.getStatus());
            }

            // 正确处理 details 列表
            if (propGrantStatusInfo.getDetails() != null) {
                List<GamePropServiceProto.PropGrantDetail> protoDetails = new ArrayList<>();
                for (PropGrantStatusResult.PropGrantDetailInfo detail : propGrantStatusInfo.getDetails()) {
                    GamePropServiceProto.PropGrantDetail protoDetail = convertPropGrantDetailInfoToProto(detail);
                    if (protoDetail != null) {
                        protoDetails.add(protoDetail);
                    }
                }
                builder.addAllDetails(protoDetails);
            }
        }

        // 设置来自 param 的字段
        if (param != null) {
            if (param.hasAppId()) {
                builder.setAppId(param.getAppId());
            }
            if (param.hasChannelGameId()) {
                builder.setGameId(param.getChannelGameId());
            }
            if (param.hasUniqueId()) {
                builder.setUniqueId(param.getUniqueId());
            }
        }

        return builder.build();
    }

}
