package fm.lizhi.ocean.seal.convert;

import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto;
import org.junit.Test;

import java.util.Date;

/**
 * 转换器测试类
 * <AUTHOR>
 */
public class ConvertTest {

    @Test
    public void testGamePropConvert() {
        // 创建测试数据
        GamePropBean bean = GamePropBean.builder()
                .id(1L)
                .channelGameId("test_game")
                .channelPropId("test_prop")
                .appId("123")
                .channelId(1L)
                .name("测试道具")
                .propDesc("测试描述")
                .type(1)
                .durationSec(3600)
                .timeliness(false)
                .iconUrl("http://test.com/icon.png")
                .remark("测试备注")
                .deleted(false)
                .operator("test_user")
                .createTime(new Date())
                .modifyTime(new Date())
                .build();

        // 测试转换
        try {
            GamePropServiceProto.GameProp proto = GamePropServiceConvert.I.convertGamePropBeanToGameProp(bean);
            System.out.println("转换成功: " + proto);
        } catch (Exception e) {
            System.err.println("转换失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
